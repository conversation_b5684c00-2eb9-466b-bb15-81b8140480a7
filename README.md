# Eagle Club Membership Website

A comprehensive membership website with authentication, payment processing, and admin management features.

## Features

### 🔐 Authentication System
- User Registration & Login/Logout
- Password Reset functionality
- Role-based access control (Member, Admin)
- JWT token authentication

### 👤 User Profile Management
- View and edit profile information
- Membership status display
- Membership upgrade/renewal options

### 💳 Membership & Payments
- Multiple membership tiers (Free/Paid/Premium)
- GCash payment gateway integration
- Payment history and invoice generation
- Membership management

### 📰 Content Management
- Member announcements and newsfeed
- Events and perks (membership-level based access)
- Admin content management

### 🛠️ Admin Panel
- User and role management
- Membership management
- Content management (announcements, events, perks)
- Payment reports and analytics

### 📞 Contact & Support
- Contact form for member inquiries
- Admin inbox for message management

## Tech Stack

### Frontend
- **React 18** - Modern UI library
- **React Router** - Client-side routing
- **Material-UI (MUI)** - Component library
- **Axios** - HTTP client
- **React Hook Form** - Form management
- **React Query** - Server state management

### Backend
- **<PERSON>vel 10** - PHP web framework
- **PHP 8.1+** - Server-side language
- **MySQL** - Database
- **Eloquent ORM** - Database ORM
- **Lara<PERSON> Sanctum** - API authentication
- **Laravel Cashier** - Payment processing
- **Laravel Mail** - Email functionality

### Payment Integration
- **GCash API** - Payment processing
- **Stripe** - Alternative payment option

## Project Structure

```
eagleclub/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom hooks
│   │   ├── services/      # API services
│   │   ├── utils/         # Utility functions
│   │   ├── context/       # React context
│   │   └── styles/        # CSS/styling
│   └── package.json
├── backend/               # Laravel backend
│   ├── app/
│   │   ├── Http/Controllers/  # API controllers
│   │   ├── Models/           # Eloquent models
│   │   ├── Middleware/       # Custom middleware
│   │   └── Services/         # Business logic
│   ├── database/
│   │   ├── migrations/       # Database migrations
│   │   └── seeders/         # Database seeders
│   ├── routes/              # API routes
│   ├── config/              # Configuration files
│   └── composer.json
├── shared/                 # Shared utilities/types
└── docs/                  # Documentation
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd eagleclub
```

2. Install dependencies for both client and server
```bash
npm run install-all
```

3. Set up environment variables
```bash
# Copy example env files
cp server/.env.example server/.env
cp client/.env.example client/.env
```

4. Configure your environment variables in the .env files

5. Start the development servers
```bash
npm run dev
```

This will start both the React frontend (port 3000) and Express backend (port 5000).

## Environment Variables

### Server (.env)
```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/eagleclub
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE=30d
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
GCASH_API_KEY=your-gcash-api-key
GCASH_SECRET=your-gcash-secret
CLIENT_URL=http://localhost:3000
```

### Client (.env)
```
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_GCASH_PUBLIC_KEY=your-gcash-public-key
```

## API Documentation

The API documentation will be available at `/api/docs` when the server is running.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
