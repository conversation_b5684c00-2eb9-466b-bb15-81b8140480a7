{"name": "eagleclub-server", "version": "1.0.0", "description": "Eagle Club Backend API", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "stripe": "^13.5.0", "axios": "^1.5.0", "moment": "^2.29.4", "express-async-handler": "^1.2.0", "morgan": "^1.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}, "keywords": ["api", "membership", "authentication", "payments"], "author": "Eagle Club", "license": "MIT"}