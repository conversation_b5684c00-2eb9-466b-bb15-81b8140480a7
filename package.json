{"name": "eagleclub", "version": "1.0.0", "description": "Eagle Club Membership Website with Authentication, Payments, and Admin Panel", "main": "client/src/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && php artisan serve", "client": "cd client && npm start", "build": "cd client && npm run build", "install-client": "cd client && npm install", "setup-backend": "cd backend && composer install && php artisan key:generate", "migrate": "cd backend && php artisan migrate", "seed": "cd backend && php artisan db:seed", "fresh": "cd backend && php artisan migrate:fresh --seed"}, "keywords": ["membership", "authentication", "payments", "admin", "react", "laravel", "php"], "author": "Eagle Club", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}