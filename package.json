{"name": "eagleclub", "version": "1.0.0", "description": "Eagle Club Membership Website with Authentication, Payments, and Admin Panel", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "start": "cd server && npm start"}, "keywords": ["membership", "authentication", "payments", "admin", "react", "nodejs"], "author": "Eagle Club", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}